const express = require("express");
const router = express.Router();
const User = require("../models/user");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const dotenv = require("dotenv");
dotenv.config();
const JWT_SECRET = process.env.JWT_SECRET;


router.get("/register", (req, res) => {
  res.render("userRegistration", {
    title: "User Registration",
    header: "User Registration",
  });
});

router.post("/register", (req, res) => {
  const { name, email, password, confirmPassword } = req.body;
  if (!name || !email || !password || !confirmPassword) {
    return res.status(400).send("Please fill all the fields");
  }
  if (password !== confirmPassword) {
    return res.status(400).send("Passwords do not match");
  }
  const hash
  const newUser = new User({
    name,
    email,
    password,
  });
  newUser.save();
  res.send("User registered successfully");
});

router.get("/login", (req, res) => {
  res.render("userLogin", { title: "User Login", header: "User Login" });
});
router.post("/login", async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).send("Please fill all the fields");
  }

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).send("Invalid email or password");
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).send("Invalid email or password");
    }

    const token = jwt.sign({ userId: user._id }, JWT_SECRET, {
      expiresIn: "1h",
    });

    res.cookie("token", token,{
      maxAge: 3600000,
    });
    res.send("User logged in successfully");
  } catch (err) {
    console.error(err);
    res.status(500).send("Server error");
  }
});



module.exports = router;
